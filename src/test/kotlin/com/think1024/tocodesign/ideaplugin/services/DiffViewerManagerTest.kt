package com.think1024.tocodesign.ideaplugin.services

import com.intellij.testFramework.fixtures.BasePlatformTestCase
import org.junit.Test

/**
 * DiffViewerManager的测试类
 */
class DiffViewerManagerTest : BasePlatformTestCase() {
    
    private lateinit var diffViewerManager: DiffViewerManager
    
    override fun setUp() {
        super.setUp()
        diffViewerManager = DiffViewerManager.getInstance(project)
    }
    
    @Test
    fun testRegisterAndUnregisterDiff() {
        val filePath = "test/TestFile.java"
        val originalContent = "public class TestFile {}"
        
        // 测试注册diff
        diffViewerManager.registerDiff(filePath, originalContent, DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT)
        assertTrue("Should have active diff", diffViewerManager.hasActiveDiff(filePath))
        
        // 测试取消注册diff
        diffViewerManager.unregisterDiff(filePath)
        assertFalse("Should not have active diff", diffViewerManager.hasActiveDiff(filePath))
    }
    
    @Test
    fun testSetDiffActive() {
        val filePath = "test/TestFile.java"
        val originalContent = "public class TestFile {}"
        
        // 注册diff
        diffViewerManager.registerDiff(filePath, originalContent, DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT)
        assertTrue("Should have active diff", diffViewerManager.hasActiveDiff(filePath))
        
        // 设置为非活跃状态
        diffViewerManager.setDiffActive(filePath, false)
        assertFalse("Should not have active diff", diffViewerManager.hasActiveDiff(filePath))
        
        // 重新设置为活跃状态
        diffViewerManager.setDiffActive(filePath, true)
        assertTrue("Should have active diff", diffViewerManager.hasActiveDiff(filePath))
    }
    
    @Test
    fun testGetActiveDiffFiles() {
        val filePath1 = "test/TestFile1.java"
        val filePath2 = "test/TestFile2.java"
        val originalContent = "public class TestFile {}"
        
        // 注册两个diff
        diffViewerManager.registerDiff(filePath1, originalContent, DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT)
        diffViewerManager.registerDiff(filePath2, originalContent, DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT)
        
        val activeDiffs = diffViewerManager.getActiveDiffFiles()
        assertEquals("Should have 2 active diffs", 2, activeDiffs.size)
        assertTrue("Should contain filePath1", activeDiffs.contains(filePath1))
        assertTrue("Should contain filePath2", activeDiffs.contains(filePath2))
        
        // 设置一个为非活跃状态
        diffViewerManager.setDiffActive(filePath1, false)
        val activeDiffsAfter = diffViewerManager.getActiveDiffFiles()
        assertEquals("Should have 1 active diff", 1, activeDiffsAfter.size)
        assertTrue("Should contain filePath2", activeDiffsAfter.contains(filePath2))
        assertFalse("Should not contain filePath1", activeDiffsAfter.contains(filePath1))
    }
    
    @Test
    fun testRefreshDiffThrottling() {
        val filePath = "test/TestFile.java"
        val originalContent = "public class TestFile {}"
        
        // 注册diff
        diffViewerManager.registerDiff(filePath, originalContent, DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT)
        
        // 第一次刷新应该成功
        diffViewerManager.refreshDiff(filePath)
        
        // 立即再次刷新应该被限制（由于频率限制）
        // 这里我们无法直接测试是否被限制，但可以确保不会抛出异常
        diffViewerManager.refreshDiff(filePath)
        
        // 等待一段时间后再次刷新
        Thread.sleep(1100) // 等待超过1秒的限制
        diffViewerManager.refreshDiff(filePath)
    }
}
