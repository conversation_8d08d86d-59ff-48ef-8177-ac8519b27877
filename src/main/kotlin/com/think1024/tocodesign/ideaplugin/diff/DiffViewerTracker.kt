package com.think1024.tocodesign.ideaplugin.diff

import com.intellij.diff.DiffContext
import com.intellij.diff.DiffExtension
import com.intellij.diff.FrameDiffTool
import com.intellij.diff.requests.DiffRequest
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.think1024.tocodesign.ideaplugin.services.DiffViewerManager
import java.util.concurrent.ConcurrentHashMap

/**
 * Diff窗口跟踪器
 * 用于跟踪已打开的diff窗口，以便后续刷新
 */
class DiffViewerTracker : DiffExtension() {
    
    private val logger = Logger.getInstance(DiffViewerTracker::class.java)
    
    companion object {
        // 用于标识我们管理的diff请求
        val TOCO_DIFF_FILE_PATH_KEY = Key.create<String>("Toco.DiffFilePath")
        val TOCO_DIFF_TYPE_KEY = Key.create<DiffViewerManager.DiffType>("Toco.DiffType")
        
        // 存储活跃的diff viewer
        private val activeDiffViewers = ConcurrentHashMap<String, FrameDiffTool.DiffViewer>()
        
        /**
         * 获取指定文件的活跃diff viewer
         */
        fun getActiveDiffViewer(filePath: String): FrameDiffTool.DiffViewer? {
            return activeDiffViewers[filePath]
        }
        
        /**
         * 获取所有活跃的diff viewer
         */
        fun getAllActiveDiffViewers(): Map<String, FrameDiffTool.DiffViewer> {
            return activeDiffViewers.toMap()
        }
        
        /**
         * 移除指定文件的diff viewer
         */
        fun removeDiffViewer(filePath: String) {
            activeDiffViewers.remove(filePath)
        }
        
        /**
         * 清除所有diff viewer
         */
        fun clearAllDiffViewers() {
            activeDiffViewers.clear()
        }
    }
    
    override fun onViewerCreated(
        viewer: FrameDiffTool.DiffViewer,
        context: DiffContext,
        request: DiffRequest
    ) {
        // 检查是否是我们管理的diff请求
        val filePath = request.getUserData(TOCO_DIFF_FILE_PATH_KEY)
        val diffType = request.getUserData(TOCO_DIFF_TYPE_KEY)
        
        if (filePath != null && diffType != null) {
            logger.info("Tracking diff viewer for file: $filePath, type: $diffType")
            
            // 存储diff viewer引用
            activeDiffViewers[filePath] = viewer
            
            // 获取项目并更新DiffViewerManager
            val project = context.project
            if (project != null) {
                val diffViewerManager = DiffViewerManager.getInstance(project)
                // 更新DiffViewerManager中的viewer引用
                updateDiffViewerManager(diffViewerManager, filePath, viewer)
            }
        }
    }
    
    // 注意：onViewerDisposed方法在某些版本的IntelliJ中可能不存在
    // 我们可以通过其他方式来处理viewer的清理
    
    /**
     * 更新DiffViewerManager中的viewer引用
     */
    private fun updateDiffViewerManager(
        diffViewerManager: DiffViewerManager,
        filePath: String,
        viewer: FrameDiffTool.DiffViewer
    ) {
        try {
            // 这里我们需要通过反射或其他方式更新DiffViewerManager中的viewer引用
            // 由于DiffViewerManager的内部实现，我们可能需要添加一个公共方法
            diffViewerManager.updateDiffViewer(filePath, viewer)
        } catch (e: Exception) {
            logger.warn("Failed to update diff viewer manager", e)
        }
    }
}

/**
 * Diff请求构建器
 * 用于创建带有跟踪信息的diff请求
 */
object TrackedDiffRequestBuilder {
    
    /**
     * 为diff请求添加跟踪信息
     */
    fun addTrackingInfo(
        request: DiffRequest,
        filePath: String,
        diffType: DiffViewerManager.DiffType
    ) {
        request.putUserData(DiffViewerTracker.TOCO_DIFF_FILE_PATH_KEY, filePath)
        request.putUserData(DiffViewerTracker.TOCO_DIFF_TYPE_KEY, diffType)
    }
}
