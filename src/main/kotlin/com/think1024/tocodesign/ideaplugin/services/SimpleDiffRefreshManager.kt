package com.think1024.tocodesign.ideaplugin.services

import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.editor.DiffContentVirtualFile
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.concurrent.ConcurrentHashMap

/**
 * 简化的Diff刷新管理器
 * 专注于解决核心问题：关闭现有diff窗口并重新打开，模拟刷新效果
 */
@Service(Service.Level.PROJECT)
class SimpleDiffRefreshManager(private val project: Project) {
    
    private val logger = Logger.getInstance(SimpleDiffRefreshManager::class.java)
    
    /**
     * 存储已注册的diff信息
     */
    private val registeredDiffs = ConcurrentHashMap<String, DiffInfo>()
    
    data class DiffInfo(
        val filePath: String,
        val originalContent: String
    )
    
    /**
     * 注册一个diff
     */
    fun registerDiff(filePath: String, originalContent: String) {
        registeredDiffs[filePath] = DiffInfo(filePath, originalContent)
        logger.info("Registered diff for file: $filePath")
    }
    
    /**
     * 取消注册diff
     */
    fun unregisterDiff(filePath: String) {
        registeredDiffs.remove(filePath)
        logger.info("Unregistered diff for file: $filePath")
    }
    
    /**
     * 刷新指定文件的diff
     */
    fun refreshDiff(filePath: String) {
        val diffInfo = registeredDiffs[filePath] ?: return
        
        logger.info("Refreshing diff for file: $filePath")
        
        ApplicationManager.getApplication().invokeLater {
            try {
                // 1. 关闭所有相关的diff窗口
                closeDiffWindowsForFile(filePath)
                
                // 2. 获取当前文件内容
                val codeFileService = CodeFileService.getInstance(project)
                val currentContent = codeFileService.getFileContent(filePath)
                
                if (currentContent == null) {
                    logger.warn("Cannot get current content for file: $filePath")
                    return@invokeLater
                }
                
                // 3. 稍微延迟后重新打开diff
                ApplicationManager.getApplication().invokeLater {
                    reopenDiff(diffInfo, currentContent)
                }
                
            } catch (e: Exception) {
                logger.warn("Failed to refresh diff for file: $filePath", e)
            }
        }
    }
    
    /**
     * 关闭指定文件相关的diff窗口
     */
    private fun closeDiffWindowsForFile(filePath: String) {
        try {
            val editorManager = FileEditorManager.getInstance(project)
            val openFiles = editorManager.openFiles.toList()
            
            val fileName = FileUtil.getVirtualFile(filePath, project)?.name
            logger.info("Looking for diff windows to close for file: $fileName")
            
            for (file in openFiles) {
                if (file is DiffContentVirtualFile) {
                    val diffFileName = file.toString()
                    logger.info("Found diff file: $diffFileName")
                    
                    // 检查是否与目标文件相关
                    if (fileName != null && (diffFileName.contains(fileName) || diffFileName.contains("diff"))) {
                        logger.info("Closing related diff file: $diffFileName")
                        editorManager.closeFile(file)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to close diff windows", e)
        }
    }
    
    /**
     * 重新打开diff
     */
    private fun reopenDiff(diffInfo: DiffInfo, currentContent: String) {
        try {
            logger.info("Reopening diff for file: ${diffInfo.filePath}")
            
            val diffRequest = SimpleDiffRequest(
                "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath}",
                DiffContentFactory.getInstance().create(diffInfo.originalContent),
                DiffContentFactory.getInstance().create(currentContent),
                getI18nString("code.diff.origin.version"),
                getI18nString("code.diff.current.version"),
            )
            
            DiffManager.getInstance().showDiff(project, diffRequest)
            logger.info("Successfully reopened diff for file: ${diffInfo.filePath}")
            
        } catch (e: Exception) {
            logger.warn("Failed to reopen diff", e)
        }
    }
    
    /**
     * 检查是否有注册的diff
     */
    fun hasRegisteredDiff(filePath: String): Boolean {
        return registeredDiffs.containsKey(filePath)
    }
    
    companion object {
        fun getInstance(project: Project): SimpleDiffRefreshManager {
            return project.getService(SimpleDiffRefreshManager::class.java)
        }
    }
}
