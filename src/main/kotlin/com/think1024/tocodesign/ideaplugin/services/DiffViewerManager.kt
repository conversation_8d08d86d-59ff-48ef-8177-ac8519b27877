package com.think1024.tocodesign.ideaplugin.services

import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.FrameDiffTool
import com.intellij.diff.editor.DiffContentVirtualFile
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.DiffDataKeys
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.WindowManager
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.awt.Component
import java.util.concurrent.ConcurrentHashMap
import javax.swing.SwingUtilities

/**
 * Diff界面管理服务
 * 负责跟踪和管理已打开的diff界面，并在文件内容变化时自动刷新
 */
@Service(Service.Level.PROJECT)
class DiffViewerManager(private val project: Project) {
    
    private val logger = Logger.getInstance(DiffViewerManager::class.java)
    
    /**
     * 存储已打开的diff界面信息
     * Key: 文件路径, Value: DiffInfo
     */
    private val activeDiffs = ConcurrentHashMap<String, DiffInfo>()
    
    /**
     * Diff界面信息
     */
    data class DiffInfo(
        val filePath: String,
        val originalContent: String,
        val diffType: DiffType,
        var isActive: Boolean = true,
        var lastRefreshTime: Long = System.currentTimeMillis(),
        var diffViewer: FrameDiffTool.DiffViewer? = null
    )
    
    /**
     * Diff类型枚举
     */
    enum class DiffType {
        ORIGINAL_VS_CURRENT,  // 原始版本 vs 当前版本
        CODE_GENERATION       // 代码生成差异
    }
    
    /**
     * 注册一个diff界面
     */
    fun registerDiff(filePath: String, originalContent: String, diffType: DiffType, diffViewer: FrameDiffTool.DiffViewer? = null) {
        val diffInfo = DiffInfo(filePath, originalContent, diffType, diffViewer = diffViewer)
        activeDiffs[filePath] = diffInfo
        logger.info("Registered diff for file: $filePath, type: $diffType")
    }
    
    /**
     * 取消注册diff界面
     */
    fun unregisterDiff(filePath: String) {
        activeDiffs.remove(filePath)?.let {
            logger.info("Unregistered diff for file: $filePath")
        }
    }
    
    /**
     * 刷新指定文件的diff界面
     */
    fun refreshDiff(filePath: String) {
        val diffInfo = activeDiffs[filePath] ?: return

        if (!diffInfo.isActive) {
            return
        }

        // 防止频繁刷新，至少间隔1秒
        val currentTime = System.currentTimeMillis()
        if (currentTime - diffInfo.lastRefreshTime < 1000) {
            logger.debug("Skipping diff refresh for $filePath - too frequent")
            return
        }

        logger.info("Refreshing diff for file: $filePath")
        diffInfo.lastRefreshTime = currentTime

        when (diffInfo.diffType) {
            DiffType.ORIGINAL_VS_CURRENT -> refreshOriginalDiff(diffInfo)
            DiffType.CODE_GENERATION -> {
                // 代码生成的diff刷新逻辑可以后续扩展
                logger.info("Code generation diff refresh not implemented yet")
            }
        }
    }
    
    /**
     * 刷新所有相关的diff界面
     */
    fun refreshAllDiffs() {
        activeDiffs.keys.forEach { filePath ->
            refreshDiff(filePath)
        }
    }
    
    /**
     * 刷新原始版本diff
     */
    private fun refreshOriginalDiff(diffInfo: DiffInfo) {
        ApplicationManager.getApplication().invokeLater {
            try {
                // 获取当前文件内容
                val codeFileService = CodeFileService.getInstance(project)
                val currentContent = codeFileService.getFileContent(diffInfo.filePath)

                if (currentContent == null) {
                    logger.warn("Cannot get current content for file: ${diffInfo.filePath}")
                    return@invokeLater
                }

                // 尝试刷新已打开的diff界面
                val refreshed = tryRefreshExistingDiffViewer(diffInfo, currentContent)

                if (!refreshed) {
                    logger.info("Could not refresh existing diff viewer, opening new one for: ${diffInfo.filePath}")
                    // 如果无法刷新现有界面，则打开新的diff界面
                    val diffRequest = SimpleDiffRequest(
                        "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath}",
                        DiffContentFactory.getInstance().create(diffInfo.originalContent),
                        DiffContentFactory.getInstance().create(currentContent),
                        getI18nString("code.diff.origin.version"),
                        getI18nString("code.diff.current.version"),
                    )

                    DiffManager.getInstance().showDiff(project, diffRequest)
                }

                // 显示刷新通知
                val fileName = FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath
                val notification = Notification(
                    "Toco Design",
                    "Diff已刷新",
                    "文件 $fileName 的差异视图已更新",
                    NotificationType.INFORMATION
                )
                Notifications.Bus.notify(notification, project)

            } catch (e: Exception) {
                logger.warn("Failed to refresh diff for file: ${diffInfo.filePath}", e)
            }
        }
    }

    /**
     * 尝试刷新已打开的diff界面
     */
    private fun tryRefreshExistingDiffViewer(diffInfo: DiffInfo, newContent: String): Boolean {
        try {
            logger.info("Attempting to refresh existing diff viewer for: ${diffInfo.filePath}")

            // 方法1: 关闭现有的diff窗口并重新打开（模拟刷新效果）
            val editorManager = FileEditorManager.getInstance(project)
            val openFiles = editorManager.openFiles.toList() // 创建副本避免并发修改

            logger.info("Found ${openFiles.size} open files")

            var foundDiffFile = false
            for (file in openFiles) {
                logger.info("Checking file: ${file.javaClass.simpleName}")

                if (file is DiffContentVirtualFile) {
                    logger.info("Found DiffContentVirtualFile: ${file}")

                    // 检查这个diff文件是否与我们要刷新的文件相关
                    if (isDiffFileRelatedToPath(file, diffInfo.filePath)) {
                        logger.info("Found related diff file, closing it")
                        foundDiffFile = true

                        // 关闭现有的diff文件
                        ApplicationManager.getApplication().invokeLater {
                            editorManager.closeFile(file)

                            // 稍微延迟后重新打开新的diff
                            ApplicationManager.getApplication().invokeLater {
                                reopenDiffWithNewContent(diffInfo, newContent)
                            }
                        }
                        return true
                    }
                }
            }

            if (!foundDiffFile) {
                logger.info("No related diff file found for: ${diffInfo.filePath}")
            }

        } catch (e: Exception) {
            logger.warn("Failed to refresh existing diff viewer for: ${diffInfo.filePath}", e)
        }

        return false
    }

    /**
     * 检查diff文件是否与指定路径相关
     */
    private fun isDiffFileRelatedToPath(diffFile: DiffContentVirtualFile, filePath: String): Boolean {
        try {
            // 简单的启发式方法：检查文件名是否包含在diff文件的名称中
            val fileName = FileUtil.getVirtualFile(filePath, project)?.name
            if (fileName != null) {
                val diffFileName = diffFile.toString()
                logger.info("Checking if diff file '$diffFileName' is related to '$fileName' or '$filePath'")
                val isRelated = diffFileName.contains(fileName) || diffFileName.contains(filePath)
                logger.info("Diff file relation check result: $isRelated")
                return isRelated
            }
        } catch (e: Exception) {
            logger.warn("Failed to check if diff file is related to path", e)
        }
        return false
    }

    /**
     * 重新打开diff并显示新内容
     */
    private fun reopenDiffWithNewContent(diffInfo: DiffInfo, newContent: String) {
        try {
            logger.info("Reopening diff with new content for: ${diffInfo.filePath}")

            val diffRequest = SimpleDiffRequest(
                "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath}",
                DiffContentFactory.getInstance().create(diffInfo.originalContent),
                DiffContentFactory.getInstance().create(newContent),
                getI18nString("code.diff.origin.version"),
                getI18nString("code.diff.current.version"),
            )

            // 添加跟踪信息
            com.think1024.tocodesign.ideaplugin.diff.TrackedDiffRequestBuilder.addTrackingInfo(
                diffRequest,
                diffInfo.filePath,
                diffInfo.diffType
            )

            DiffManager.getInstance().showDiff(project, diffRequest)

        } catch (e: Exception) {
            logger.warn("Failed to reopen diff with new content", e)
        }
    }

    /**
     * 在编辑器中查找diff viewer
     */
    private fun findDiffViewerInEditor(editor: com.intellij.openapi.fileEditor.FileEditor): FrameDiffTool.DiffViewer? {
        try {
            // 尝试从编辑器组件中查找diff viewer
            val component = editor.component
            return findDiffViewerInComponent(component)
        } catch (e: Exception) {
            logger.warn("Failed to find diff viewer in editor", e)
            return null
        }
    }

    /**
     * 在组件中递归查找diff viewer
     */
    private fun findDiffViewerInComponent(component: Component): FrameDiffTool.DiffViewer? {
        // 检查当前组件是否是diff viewer
        if (component is SimpleDiffViewer) {
            return component
        } else if (component is UnifiedDiffViewer) {
            return component
        }

        // 递归检查子组件
        if (component is java.awt.Container) {
            for (child in component.components) {
                val found = findDiffViewerInComponent(child)
                if (found != null) {
                    return found
                }
            }
        }

        return null
    }

    /**
     * 刷新diff viewer
     */
    private fun refreshDiffViewer(viewer: FrameDiffTool.DiffViewer, diffInfo: DiffInfo, newContent: String): Boolean {
        return when (viewer) {
            is SimpleDiffViewer -> refreshSimpleDiffViewer(viewer, diffInfo, newContent)
            is UnifiedDiffViewer -> refreshUnifiedDiffViewer(viewer, diffInfo, newContent)
            else -> {
                logger.warn("Unsupported diff viewer type: ${viewer.javaClass.simpleName}")
                false
            }
        }
    }

    /**
     * 刷新DiffContentVirtualFile的内容
     */
    private fun refreshDiffContentFile(file: DiffContentVirtualFile, diffInfo: DiffInfo, newContent: String): Boolean {
        try {
            // 这里需要更深入的实现来更新diff内容
            // 由于DiffContentVirtualFile的限制，我们可能需要通过其他方式
            logger.debug("Attempting to refresh DiffContentVirtualFile: ${file.toString()}")
            return false
        } catch (e: Exception) {
            logger.warn("Failed to refresh DiffContentVirtualFile", e)
            return false
        }
    }

    /**
     * 在窗口中查找并刷新diff组件
     */
    private fun findAndRefreshDiffInWindow(component: Component, diffInfo: DiffInfo, newContent: String): Boolean {
        try {
            // 递归查找diff viewer组件
            return findDiffViewerInComponent(component, diffInfo, newContent)
        } catch (e: Exception) {
            logger.warn("Failed to find diff viewer in window", e)
            return false
        }
    }

    /**
     * 在组件树中递归查找diff viewer
     */
    private fun findDiffViewerInComponent(component: Component, diffInfo: DiffInfo, newContent: String): Boolean {
        // 检查当前组件是否是diff viewer
        if (component is SimpleDiffViewer) {
            return refreshSimpleDiffViewer(component, diffInfo, newContent)
        } else if (component is UnifiedDiffViewer) {
            return refreshUnifiedDiffViewer(component, diffInfo, newContent)
        }

        // 递归检查子组件
        if (component is java.awt.Container) {
            for (child in component.components) {
                if (findDiffViewerInComponent(child, diffInfo, newContent)) {
                    return true
                }
            }
        }

        return false
    }

    /**
     * 刷新SimpleDiffViewer
     */
    private fun refreshSimpleDiffViewer(viewer: SimpleDiffViewer, diffInfo: DiffInfo, newContent: String): Boolean {
        try {
            // 更新右侧编辑器的内容（当前版本）
            val rightEditor = viewer.editor2
            if (rightEditor != null) {
                ApplicationManager.getApplication().runWriteAction {
                    rightEditor.document.setText(newContent)
                }
                // 触发重新计算diff
                viewer.rediff()
                return true
            }
        } catch (e: Exception) {
            logger.warn("Failed to refresh SimpleDiffViewer", e)
        }
        return false
    }

    /**
     * 刷新UnifiedDiffViewer
     */
    private fun refreshUnifiedDiffViewer(viewer: UnifiedDiffViewer, diffInfo: DiffInfo, newContent: String): Boolean {
        try {
            // 更新unified diff viewer的内容
            val document = viewer.getDocument(com.intellij.diff.util.Side.RIGHT)
            if (document != null) {
                ApplicationManager.getApplication().runWriteAction {
                    document.setText(newContent)
                }
                viewer.rediff()
                return true
            }
        } catch (e: Exception) {
            logger.warn("Failed to refresh UnifiedDiffViewer", e)
        }
        return false
    }

    /**
     * 检查文件是否有活跃的diff界面
     */
    fun hasActiveDiff(filePath: String): Boolean {
        return activeDiffs[filePath]?.isActive == true
    }
    
    /**
     * 设置diff界面的活跃状态
     */
    fun setDiffActive(filePath: String, active: Boolean) {
        activeDiffs[filePath]?.let {
            it.isActive = active
        }
    }

    /**
     * 更新diff viewer引用
     */
    fun updateDiffViewer(filePath: String, viewer: FrameDiffTool.DiffViewer) {
        activeDiffs[filePath]?.let {
            it.diffViewer = viewer
            logger.info("Updated diff viewer reference for file: $filePath")
        }
    }
    
    /**
     * 获取所有活跃的diff文件路径
     */
    fun getActiveDiffFiles(): Set<String> {
        return activeDiffs.filter { it.value.isActive }.keys
    }
    
    companion object {
        fun getInstance(project: Project): DiffViewerManager {
            return project.getService(DiffViewerManager::class.java)
        }
    }
}
