package com.think1024.tocodesign.ideaplugin.services

import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.concurrent.ConcurrentHashMap

/**
 * Diff界面管理服务
 * 负责跟踪和管理已打开的diff界面，并在文件内容变化时自动刷新
 */
@Service(Service.Level.PROJECT)
class DiffViewerManager(private val project: Project) {
    
    private val logger = Logger.getInstance(DiffViewerManager::class.java)
    
    /**
     * 存储已打开的diff界面信息
     * Key: 文件路径, Value: DiffInfo
     */
    private val activeDiffs = ConcurrentHashMap<String, DiffInfo>()
    
    /**
     * Diff界面信息
     */
    data class DiffInfo(
        val filePath: String,
        val originalContent: String,
        val diffType: DiffType,
        var isActive: Boolean = true,
        var lastRefreshTime: Long = System.currentTimeMillis()
    )
    
    /**
     * Diff类型枚举
     */
    enum class DiffType {
        ORIGINAL_VS_CURRENT,  // 原始版本 vs 当前版本
        CODE_GENERATION       // 代码生成差异
    }
    
    /**
     * 注册一个diff界面
     */
    fun registerDiff(filePath: String, originalContent: String, diffType: DiffType) {
        val diffInfo = DiffInfo(filePath, originalContent, diffType)
        activeDiffs[filePath] = diffInfo
        logger.info("Registered diff for file: $filePath, type: $diffType")
    }
    
    /**
     * 取消注册diff界面
     */
    fun unregisterDiff(filePath: String) {
        activeDiffs.remove(filePath)?.let {
            logger.info("Unregistered diff for file: $filePath")
        }
    }
    
    /**
     * 刷新指定文件的diff界面
     */
    fun refreshDiff(filePath: String) {
        val diffInfo = activeDiffs[filePath] ?: return

        if (!diffInfo.isActive) {
            return
        }

        // 防止频繁刷新，至少间隔1秒
        val currentTime = System.currentTimeMillis()
        if (currentTime - diffInfo.lastRefreshTime < 1000) {
            logger.debug("Skipping diff refresh for $filePath - too frequent")
            return
        }

        logger.info("Refreshing diff for file: $filePath")
        diffInfo.lastRefreshTime = currentTime

        when (diffInfo.diffType) {
            DiffType.ORIGINAL_VS_CURRENT -> refreshOriginalDiff(diffInfo)
            DiffType.CODE_GENERATION -> {
                // 代码生成的diff刷新逻辑可以后续扩展
                logger.info("Code generation diff refresh not implemented yet")
            }
        }
    }
    
    /**
     * 刷新所有相关的diff界面
     */
    fun refreshAllDiffs() {
        activeDiffs.keys.forEach { filePath ->
            refreshDiff(filePath)
        }
    }
    
    /**
     * 刷新原始版本diff
     */
    private fun refreshOriginalDiff(diffInfo: DiffInfo) {
        ApplicationManager.getApplication().invokeLater {
            try {
                // 获取当前文件内容
                val codeFileService = CodeFileService.getInstance(project)
                val currentContent = codeFileService.getFileContent(diffInfo.filePath)
                
                if (currentContent == null) {
                    logger.warn("Cannot get current content for file: ${diffInfo.filePath}")
                    return@invokeLater
                }
                
                // 创建新的diff请求
                val diffRequest = SimpleDiffRequest(
                    "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath}",
                    DiffContentFactory.getInstance().create(diffInfo.originalContent),
                    DiffContentFactory.getInstance().create(currentContent),
                    getI18nString("code.diff.origin.version"),
                    getI18nString("code.diff.current.version"),
                )
                
                // 显示新的diff
                DiffManager.getInstance().showDiff(project, diffRequest)

                // 显示刷新通知
                val fileName = FileUtil.getVirtualFile(diffInfo.filePath, project)?.name ?: diffInfo.filePath
                val notification = Notification(
                    "Toco Design",
                    "Diff已刷新",
                    "文件 $fileName 的差异视图已更新",
                    NotificationType.INFORMATION
                )
                Notifications.Bus.notify(notification, project)

            } catch (e: Exception) {
                logger.warn("Failed to refresh diff for file: ${diffInfo.filePath}", e)
            }
        }
    }
    
    /**
     * 检查文件是否有活跃的diff界面
     */
    fun hasActiveDiff(filePath: String): Boolean {
        return activeDiffs[filePath]?.isActive == true
    }
    
    /**
     * 设置diff界面的活跃状态
     */
    fun setDiffActive(filePath: String, active: Boolean) {
        activeDiffs[filePath]?.let {
            it.isActive = active
        }
    }
    
    /**
     * 获取所有活跃的diff文件路径
     */
    fun getActiveDiffFiles(): Set<String> {
        return activeDiffs.filter { it.value.isActive }.keys
    }
    
    companion object {
        fun getInstance(project: Project): DiffViewerManager {
            return project.getService(DiffViewerManager::class.java)
        }
    }
}
