# Diff界面自动刷新功能实现总结

## 🎯 需求背景

用户反馈：通过DiffManager打开的diff界面，在文件的内容发生变化之后（比如用户操作了discard，调用了`discardChangesForSingleFile`），需要及时刷新diff的结果。

**核心问题**：原有实现每次都会重新打开新的diff窗口，而不是刷新已经打开的diff界面。

## ✅ 解决方案

### 核心改进

1. **真正的界面刷新**：能够直接更新已打开的diff界面内容，而不是重新打开新窗口
2. **智能跟踪机制**：通过自定义DiffExtension跟踪已打开的diff窗口
3. **多重刷新策略**：提供多种刷新方式，确保兼容性

### 技术架构

```
┌─────────────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│   FileOriginalVersion │    │   DiffViewerManager  │    │  DiffViewerTracker  │
│       Service        │    │                      │    │   (DiffExtension)   │
└─────────────────────┘    └──────────────────────┘    └─────────────────────┘
           │                           │                           │
           │ 1. showOriginalDiff()     │                           │
           ├──────────────────────────►│                           │
           │                           │ 2. registerDiff()         │
           │                           ├──────────────────────────►│
           │                           │                           │
           │                           │ 3. onViewerCreated()      │
           │                           │◄──────────────────────────┤
           │                           │                           │
┌─────────────────────┐                │                           │
│   CodeFileService   │                │                           │
└─────────────────────┘                │                           │
           │                           │                           │
           │ 4. discardChanges()       │                           │
           ├──────────────────────────►│                           │
           │                           │ 5. refreshDiff()          │
           │                           │   - 直接更新diff内容      │
           │                           │   - 调用viewer.rediff()   │
```

## 🔧 实现细节

### 1. DiffViewerManager服务

**核心功能**：
- 跟踪已打开的diff界面
- 提供真正的diff界面刷新功能
- 管理diff界面的生命周期

**关键方法**：
```kotlin
// 注册diff界面（支持viewer引用）
fun registerDiff(filePath: String, originalContent: String, diffType: DiffType, diffViewer: FrameDiffTool.DiffViewer? = null)

// 刷新指定文件的diff界面（真正的刷新）
fun refreshDiff(filePath: String)

// 更新diff viewer引用
fun updateDiffViewer(filePath: String, viewer: FrameDiffTool.DiffViewer)
```

### 2. DiffViewerTracker扩展

**核心功能**：
- 通过DiffExtension机制跟踪diff窗口的创建
- 自动注册diff viewer到管理器
- 提供全局的diff viewer访问

**关键特性**：
```kotlin
// 在diff请求中添加跟踪标识
val TOCO_DIFF_FILE_PATH_KEY = Key.create<String>("Toco.DiffFilePath")
val TOCO_DIFF_TYPE_KEY = Key.create<DiffViewerManager.DiffType>("Toco.DiffType")

// 跟踪活跃的diff viewer
private val activeDiffViewers = ConcurrentHashMap<String, FrameDiffTool.DiffViewer>()
```

### 3. 多重刷新策略

**策略优先级**：
1. **优先策略**：使用存储的diff viewer引用直接刷新
2. **备用策略**：通过DiffViewerTracker查找活跃的diff窗口
3. **兜底策略**：重新打开diff窗口（保证兼容性）

**支持的Viewer类型**：
```kotlin
// SimpleDiffViewer刷新
private fun refreshSimpleDiffViewer(viewer: SimpleDiffViewer, diffInfo: DiffInfo, newContent: String): Boolean {
    val rightEditor = viewer.editor2
    if (rightEditor != null) {
        ApplicationManager.getApplication().runWriteAction {
            rightEditor.document.setText(newContent)
        }
        viewer.rediff()  // 触发重新计算diff
        return true
    }
    return false
}

// UnifiedDiffViewer刷新
private fun refreshUnifiedDiffViewer(viewer: UnifiedDiffViewer, diffInfo: DiffInfo, newContent: String): Boolean {
    val document = viewer.getDocument(Side.RIGHT)
    if (document != null) {
        ApplicationManager.getApplication().runWriteAction {
            document.setText(newContent)
        }
        viewer.rediff()  // 触发重新计算diff
        return true
    }
    return false
}
```

## 📋 使用方式

### 自动模式（推荐）

```kotlin
// 1. 显示diff（自动注册并跟踪）
val fileOriginalVersionService = FileOriginalVersionService.getInstance(project)
fileOriginalVersionService.showOriginalDiff("path/to/file.java")

// 2. 执行discard操作（自动刷新已打开的diff界面）
val codeFileService = CodeFileService.getInstance(project)
codeFileService.discardChangesForSingleFile("path/to/file.java")
// ✅ 此时已打开的diff界面会自动刷新显示最新内容
```

### 手动模式

```kotlin
val diffViewerManager = DiffViewerManager.getInstance(project)

// 手动注册diff
diffViewerManager.registerDiff(filePath, originalContent, DiffType.ORIGINAL_VS_CURRENT)

// 手动刷新diff（直接更新界面内容）
diffViewerManager.refreshDiff(filePath)
```

## 🚀 核心优势

1. **真正的界面刷新**：不再重新打开新窗口，直接更新现有diff界面
2. **用户体验提升**：无缝的diff内容更新，保持用户的操作上下文
3. **性能优化**：避免重复创建diff窗口的开销
4. **智能跟踪**：自动管理diff窗口的生命周期
5. **兼容性保证**：多重刷新策略确保在各种情况下都能正常工作

## 📁 文件结构

```
src/main/kotlin/com/think1024/tocodesign/ideaplugin/
├── services/
│   ├── DiffViewerManager.kt          # diff界面管理服务
│   ├── FileOriginalVersionService.kt # 集成diff跟踪
│   └── CodeFileService.kt            # 触发diff刷新
├── diff/
│   └── DiffViewerTracker.kt          # diff窗口跟踪扩展
└── resources/META-INF/
    └── plugin.xml                    # 注册DiffExtension

docs/
└── diff-refresh-feature.md           # 详细功能文档

examples/
└── DiffRefreshExample.kt             # 使用示例

tests/
└── DiffViewerManagerTest.kt          # 单元测试
```

## 🎉 总结

这个实现完全解决了用户提出的需求：

- ✅ **需求满足**：在文件内容变化后，已打开的diff界面能够及时刷新
- ✅ **技术突破**：实现了真正的diff界面刷新，而不是重新打开窗口
- ✅ **用户体验**：提供了无缝的diff内容更新体验
- ✅ **扩展性**：支持多种diff类型和刷新策略
- ✅ **稳定性**：多重保障机制确保功能的可靠性

现在用户在执行discard操作后，相关的diff界面会自动刷新显示最新的差异结果，大大提升了开发效率和用户体验！
