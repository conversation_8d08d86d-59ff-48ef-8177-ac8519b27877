# Diff界面自动刷新功能

## 功能概述

这个功能实现了在文件内容发生变化后（比如用户操作了discard，调用了`discardChangesForSingleFile`），自动刷新已打开的diff界面，让用户能够实时看到最新的差异结果。

## 实现原理

### 核心组件

1. **DiffViewerManager** - diff界面管理服务
   - 跟踪已打开的diff界面
   - 管理diff界面的生命周期
   - 提供diff刷新功能

2. **FileOriginalVersionService** - 文件原始版本服务
   - 集成了DiffViewerManager
   - 在显示diff时自动注册到管理器
   - 在清除原始版本时自动取消注册

3. **CodeFileService** - 代码文件服务
   - 在文件内容变化后触发diff刷新
   - 支持单文件和批量文件的刷新

### 工作流程

1. **注册阶段**：当用户通过`FileOriginalVersionService.showOriginalDiff()`打开diff界面时，系统会：
   - 将diff信息注册到`DiffViewerManager`
   - 记录文件路径、原始内容和diff类型

2. **监听阶段**：系统持续跟踪已注册的diff界面状态

3. **刷新阶段**：当文件内容发生变化时（如调用`discardChangesForSingleFile`），系统会：
   - 检查是否有该文件的活跃diff界面
   - 如果有，则自动刷新diff内容
   - 显示刷新通知给用户

4. **清理阶段**：当原始版本被清除时，自动取消注册diff界面

## 使用方法

### 基本使用

```kotlin
// 1. 显示diff（会自动注册到管理器）
val fileOriginalVersionService = FileOriginalVersionService.getInstance(project)
fileOriginalVersionService.showOriginalDiff("path/to/file.java")

// 2. 执行文件变更操作（会自动刷新diff）
val codeFileService = CodeFileService.getInstance(project)
codeFileService.discardChangesForSingleFile("path/to/file.java")
```

### 高级使用

```kotlin
// 直接使用DiffViewerManager
val diffViewerManager = DiffViewerManager.getInstance(project)

// 手动注册diff
diffViewerManager.registerDiff(
    filePath = "path/to/file.java",
    originalContent = "original content",
    diffType = DiffViewerManager.DiffType.ORIGINAL_VS_CURRENT
)

// 手动刷新diff
diffViewerManager.refreshDiff("path/to/file.java")

// 检查是否有活跃的diff
if (diffViewerManager.hasActiveDiff("path/to/file.java")) {
    // 执行相关操作
}

// 手动取消注册
diffViewerManager.unregisterDiff("path/to/file.java")
```

## 特性

### 防抖机制
- 实现了1秒的刷新间隔限制，防止频繁刷新
- 避免在短时间内多次触发刷新操作

### 通知机制
- 当diff被刷新时，会显示信息通知
- 告知用户哪个文件的diff已经更新

### 生命周期管理
- 自动管理diff界面的注册和取消注册
- 与文件原始版本服务集成，确保数据一致性

## 扩展性

### 支持不同类型的diff
```kotlin
enum class DiffType {
    ORIGINAL_VS_CURRENT,  // 原始版本 vs 当前版本
    CODE_GENERATION       // 代码生成差异（可扩展）
}
```

### 批量操作支持
- 支持刷新所有活跃的diff界面
- 支持批量文件的diff刷新

## 注意事项

1. **性能考虑**：由于IntelliJ的DiffManager API限制，每次刷新实际上是打开一个新的diff窗口
2. **内存管理**：系统会自动清理不再需要的diff注册信息
3. **线程安全**：使用ConcurrentHashMap确保多线程环境下的安全性

## 未来改进

1. **更智能的刷新机制**：研究是否可以直接更新现有diff窗口的内容而不是重新打开
2. **用户偏好设置**：允许用户配置是否启用自动刷新功能
3. **更多diff类型支持**：扩展支持更多类型的diff场景
